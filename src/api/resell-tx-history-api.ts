import { collection, getDocs, orderBy, query } from 'firebase/firestore';

import {
  type ResellTxHistoryEntity,
  type UserEntity,
} from '@/constants/core.constants';
import { firestore } from '@/root-context';

import { getUsersByIds } from './user-api';

export interface ResellTxHistoryWithUsers {
  id: string;
  order_id: string;
  execution_price: string;
  reseller_id: string;
  executed_at: Date;
  buyer_id: string;
  reseller?: UserEntity;
  buyer?: UserEntity;
}

export interface ResellHistoryResponse {
  success: boolean;
  transactions: ResellTxHistoryWithUsers[];
  message?: string;
}

export const getResellHistoryByOrderId = async (
  orderId: string,
): Promise<ResellHistoryResponse> => {
  try {
    if (!orderId) {
      return {
        success: false,
        transactions: [],
        message: 'Order ID is required',
      };
    }

    // Query the resell transaction history subcollection under the specific order
    const q = query(
      collection(firestore, 'orders', orderId, 'resell_tx_history'),
      orderBy('executed_at', 'desc'),
    );

    const snapshot = await getDocs(q);
    const transactions: ResellTxHistoryEntity[] = [];

    snapshot.forEach((doc) => {
      const data = doc.data() as Omit<ResellTxHistoryEntity, 'id'>;
      transactions.push({
        id: doc.id,
        ...data,
        executed_at:
          data.executed_at instanceof Date
            ? data.executed_at
            : ((data.executed_at as any)?.toDate?.() ?? new Date()),
      });
    });

    if (transactions.length === 0) {
      return {
        success: true,
        transactions: [],
        message: 'No resell history found for this order',
      };
    }

    const userIds = new Set<string>();
    transactions.forEach((tx) => {
      userIds.add(tx.reseller_id);
      userIds.add(tx.buyer_id);
    });

    const users = await getUsersByIds(Array.from(userIds));
    const userMap = new Map<string, UserEntity>();
    users.forEach((user) => {
      userMap.set(user.id, user);
    });

    const transactionsWithUsers: ResellTxHistoryWithUsers[] = transactions
      .filter((tx) => tx.id !== undefined)
      .map((tx) => ({
        ...tx,
        id: tx.id!,
        reseller: userMap.get(tx.reseller_id),
        buyer: userMap.get(tx.buyer_id),
      }));

    return {
      success: true,
      transactions: transactionsWithUsers,
    };
  } catch (error) {
    console.error('Error fetching resell history:', error);
    return {
      success: false,
      transactions: [],
      message: 'Failed to fetch resell history',
    };
  }
};
