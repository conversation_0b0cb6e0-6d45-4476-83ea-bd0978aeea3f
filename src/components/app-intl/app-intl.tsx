import type { PropsWithChildren } from 'react';
import { IntlProvider } from 'react-intl';

import { useRootContext } from '@/root-context';

import { AppLocale } from '../../constants/core.constants';

export type AppIntlProps = {
  enMessages: Record<string, string>;
  ruMessages: Record<string, string>;
};

export const AppIntl = ({
  enMessages,
  ruMessages,
  children,
}: PropsWithChildren<AppIntlProps>) => {
  const { locale } = useRootContext();
  const messages = {
    [AppLocale.en]: enMessages,
    [AppLocale.ru]: ruMessages,
  };

  return (
    <IntlProvider
      {...{
        locale: locale,
        messages: messages[locale],
      }}
    >
      {children}
    </IntlProvider>
  );
};
