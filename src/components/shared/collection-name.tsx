import { useIntl } from 'react-intl';

import type { CollectionEntity } from '@/constants/core.constants';

import { collectionNameMessages } from './collection-name/intl/collection-name.messages';

interface CollectionNameProps {
  collection: CollectionEntity | null | undefined;
  className?: string;
  fallback?: string;
}

export function CollectionName({
  collection,
  className = '',
  fallback,
}: CollectionNameProps) {
  const { formatMessage: t } = useIntl();

  const defaultFallback =
    fallback || t(collectionNameMessages.unknownCollection);

  return (
    <span className={className}>{collection?.name || defaultFallback}</span>
  );
}
