'use client';

import { Input as TgInput } from '@telegram-apps/telegram-ui';
import { useIntl } from 'react-intl';
import { useLocalStorage } from 'usehooks-ts';

import { CollectionSelect } from '@/components/ui/collection-select';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { LocalStorageKeys } from '@/constants/storage.constants';
import { useOrderListFilters } from '@/contexts/OrderListFiltersContext';

import { marketplaceFiltersMessages } from './marketplace-filters.messages';

export const MarketplaceFilters = () => {
  const { formatMessage: t } = useIntl();
  const {
    filters,
    collections,
    setMinPrice,
    setMaxPrice,
    setSelectedCollection,
    setSortBy,
  } = useOrderListFilters();
  const [isAnimatedCollection] = useLocalStorage(
    LocalStorageKeys.IS_ANIMATED_COLLECTION,
    false,
  );

  const handleMinPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMinPrice(value);
  };

  const handleMaxPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMaxPrice(value);
  };

  return (
    <div className="flex flex-wrap items-end gap-2 py-3 rounded-lg">
      <div className="flex-1 min-w-[130px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header={t(marketplaceFiltersMessages.min)}
            placeholder="0"
            value={filters.minPrice}
            onChange={handleMinPriceChange}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]! [&+h6]:max-w-[100px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[130px]">
        <div className="[&>div]:p-0!">
          <TgInput
            type="number"
            header={t(marketplaceFiltersMessages.max)}
            placeholder="0"
            value={filters.maxPrice}
            onChange={handleMaxPriceChange}
            min="0"
            step="0.01"
            className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
          />
        </div>
      </div>

      <div className="flex-1 min-w-[130px] mt-2">
        <CollectionSelect
          animated={isAnimatedCollection}
          collections={collections}
          value={filters.selectedCollection}
          onValueChange={setSelectedCollection}
          placeholder={t(marketplaceFiltersMessages.allCollections)}
        />
      </div>

      <div className="flex-1 min-w-[130px] mt-2">
        <Select value={filters.sortBy} onValueChange={setSortBy}>
          <SelectTrigger>
            <SelectValue placeholder={t(marketplaceFiltersMessages.sortBy)} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="date_desc">
              {t(marketplaceFiltersMessages.newestFirst)}
            </SelectItem>
            <SelectItem value="date_asc">
              {t(marketplaceFiltersMessages.oldestFirst)}
            </SelectItem>
            <SelectItem value="price_desc">
              {t(marketplaceFiltersMessages.priceHighToLow)}
            </SelectItem>
            <SelectItem value="price_asc">
              {t(marketplaceFiltersMessages.priceLowToHigh)}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
