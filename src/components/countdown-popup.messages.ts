import { defineMessages } from 'react-intl';

export const countdownPopupMessages = defineMessages({
  depositProcessing: {
    id: 'countdownPopup.depositProcessing',
    defaultMessage: 'Deposit Processing',
  },
  youWillReceiveFundsWithin: {
    id: 'countdownPopup.youWillReceiveFundsWithin',
    defaultMessage: 'You will receive your funds within',
  },
  closeNotification: {
    id: 'countdownPopup.closeNotification',
    defaultMessage: 'Close notification',
  },
  minutes: {
    id: 'countdownPopup.minutes',
    defaultMessage: 'minutes',
  },
});
