import { defineMessages } from 'react-intl';

export const orderDetailsFeesMessages = defineMessages({
  orderDetailsAndFees: {
    id: 'orderDetails.fees.orderDetailsAndFees',
    defaultMessage: 'Order Details & Fees',
  },
  purchaseFee: {
    id: 'orderDetails.fees.purchaseFee',
    defaultMessage: 'Purchase Fee',
  },
  collateral: {
    id: 'orderDetails.fees.collateral',
    defaultMessage: 'Collateral',
  },
  buyer: {
    id: 'orderDetails.fees.buyer',
    defaultMessage: 'Buyer',
  },
  seller: {
    id: 'orderDetails.fees.seller',
    defaultMessage: 'Seller',
  },
  deposited: {
    id: 'orderDetails.fees.deposited',
    defaultMessage: 'Deposited',
  },
  feePaidBySeller: {
    id: 'orderDetails.fees.feePaidBySeller',
    defaultMessage: 'Fee {feePercent}%. Paid by seller.',
  },
  collateralDescription: {
    id: 'orderDetails.fees.collateralDescription',
    defaultMessage:
      '{buyerPercentage}% collateral for buyers. Locked until the order is fulfilled. Instantly refunded if order is unsuccessful.',
  },
});
