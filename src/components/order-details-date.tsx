'use client';

import { format } from 'date-fns';
import { enUS, ru } from 'date-fns/locale';
import type { Timestamp } from 'firebase/firestore';
import { useIntl } from 'react-intl';

import { AppLocale } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import { firebaseTimestampToDate } from '@/utils/date-utils';

import { orderDetailsDateMessages } from './order-details-date/intl/order-details-date.messages';

interface OrderDetailsDateProps {
  updatedAt?: Date | Timestamp;
  className?: string;
}

const localeMap = {
  [AppLocale.en]: enUS,
  [AppLocale.ru]: ru,
};

export function OrderDetailsDate({
  updatedAt,
  className,
}: OrderDetailsDateProps) {
  const { formatMessage: t } = useIntl();
  const { locale } = useRootContext();

  if (!updatedAt) {
    return null;
  }

  const date = firebaseTimestampToDate(updatedAt);
  const dateLocale = localeMap[locale] || enUS;

  const formattedDate = format(date, 'MMM d, yyyy HH:mm', {
    locale: dateLocale,
  });

  return (
    <div className={className}>
      <div className="flex justify-between items-center py-2">
        <span className="text-[#708499] text-sm">
          {t(orderDetailsDateMessages.lastUpdate)}
        </span>
        <span className="text-[#f5f5f5] text-sm font-medium">
          {formattedDate}
        </span>
      </div>
    </div>
  );
}
