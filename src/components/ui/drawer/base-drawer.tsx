'use client';

import type { ReactNode } from 'react';
import { Drawer } from 'vaul';

interface BaseDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: ReactNode;
  className?: string;
  contentClassName?: string;
  zIndex?: number;
  height?: string;
  shouldScaleBackground?: boolean;
  modal?: boolean;
  dismissible?: boolean;
}

export function BaseDrawer({
  open,
  onOpenChange,
  children,
  className = '',
  contentClassName = '',
  zIndex = 100,
  height = 'max-h-[90vh]',
  shouldScaleBackground = true,
  modal = true,
  dismissible = true,
}: BaseDrawerProps) {
  // Map zIndex to static Tailwind classes
  const getZIndexClasses = (z: number) => {
    switch (z) {
      case 50:
        return { overlay: 'z-50', content: 'z-[51]' };
      case 60:
        return { overlay: 'z-[60]', content: 'z-[61]' };
      case 70:
        return { overlay: 'z-[70]', content: 'z-[71]' };
      case 80:
        return { overlay: 'z-[80]', content: 'z-[81]' };
      case 90:
        return { overlay: 'z-[90]', content: 'z-[91]' };
      case 100:
        return { overlay: 'z-[100]', content: 'z-[101]' };
      default:
        return { overlay: 'z-[100]', content: 'z-[101]' };
    }
  };

  const zIndexClasses = getZIndexClasses(zIndex);

  return (
    <Drawer.Root
      {...{
        shouldScaleBackground,
        modal,
        dismissible,
        onOpenChange,
        open,
      }}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay
          className={`fixed inset-0 bg-black/40 ${zIndexClasses.overlay}`}
        />
        <Drawer.Content
          className={`bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 ${zIndexClasses.content} outline-none focus:outline-none ${className}`}
        >
          <div
            className={`p-6 bg-[#17212b] rounded-t-[20px] flex-1 ${height} overflow-y-auto ${contentClassName}`}
          >
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />
            <div className="max-w-md mx-auto space-y-6">{children}</div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
