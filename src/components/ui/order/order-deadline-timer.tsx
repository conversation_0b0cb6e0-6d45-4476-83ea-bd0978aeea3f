import { Clock } from 'lucide-react';
import { useIntl } from 'react-intl';

import type {
  CollectionEntity,
  OrderEntity,
  UserType,
} from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';

import { orderDeadlineTimerMessages } from './order-deadline-timer/intl/order-deadline-timer.messages';

interface OrderDeadlineTimerProps {
  order: OrderEntity;
  collection: CollectionEntity | null;
  userType: UserType;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function OrderDeadlineTimer({
  order,
  collection,
  userType,
  className = '',
  size = 'sm',
}: OrderDeadlineTimerProps) {
  const { formatMessage: t } = useIntl();
  const { timeLeft } = useOrderTimers({ order, collection });

  if (order.status !== OrderStatus.PAID) {
    return null;
  }

  const sizeClasses = {
    sm: {
      container: 'p-1.5 text-[10px]',
      icon: 'w-2.5 h-2.5',
      time: 'text-xs',
      description: 'text-[9px]',
    },
    md: {
      container: 'p-3 text-sm',
      icon: 'w-4 h-4',
      time: 'text-sm',
      description: 'text-xs',
    },
    lg: {
      container: 'p-4 text-base',
      icon: 'w-5 h-5',
      time: 'text-base',
      description: 'text-sm',
    },
  };

  const classes = sizeClasses[size];

  const getDeadlineDescription = () => {
    if (order.status === OrderStatus.PAID) {
      if (userType === 'seller')
        return t(orderDeadlineTimerMessages.sendOrLoseCollateral);
      return t(orderDeadlineTimerMessages.sellerMustSend);
    }

    return '';
  };

  return (
    <div
      className={`bg-orange-500/10 border border-orange-500/20 rounded ${classes.container} ${className}`}
    >
      {timeLeft ? (
        <>
          <div className="flex items-center gap-1 mb-0.5">
            <Clock className={`${classes.icon} text-orange-400`} />
            <span className="text-orange-400 font-medium">
              {t(orderDeadlineTimerMessages.deadline)}
            </span>
          </div>
          <div className={`text-[#f5f5f5] font-mono ${classes.time}`}>
            {timeLeft}
          </div>
          <div className={`text-[#708499] ${classes.description}`}>
            {getDeadlineDescription()}
          </div>
        </>
      ) : (
        <>
          <div className="flex items-center gap-1 mb-0.5">
            <Clock className={`${classes.icon} text-orange-400`} />
            <span className="text-orange-400 font-medium">
              {t(orderDeadlineTimerMessages.waiting)}
            </span>
          </div>
          <div className={`text-[#708499] ${classes.description}]`}>
            {t(orderDeadlineTimerMessages.giftWillBecomeTransferable)}
          </div>
        </>
      )}
    </div>
  );
}
