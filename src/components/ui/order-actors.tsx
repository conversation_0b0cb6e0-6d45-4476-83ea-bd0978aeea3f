'use client';

import { Avatar } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useIntl } from 'react-intl';

import { getUserById } from '@/api/auth-api';
import type { UserEntity } from '@/constants/core.constants';
import { ADMIN_DEFAULT_NAME } from '@/constants/core.constants';
import { cn } from '@/lib/utils';

import { orderActorsMessages } from './order-actors/intl/order-actors.messages';

interface OrderActorsProps {
  buyerId?: string;
  sellerId?: string;
  isOpen: boolean;
  isResellOrder?: boolean;
}

interface UserInfoSectionProps {
  userId: string;
  role: 'Buyer' | 'Seller' | 'Resseller';
  isOpen: boolean;
}

function UserInfoSection({ userId, role, isOpen }: UserInfoSectionProps) {
  const { formatMessage: t } = useIntl();
  const [userInfo, setUserInfo] = useState<UserEntity | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadUserInfo = async () => {
      if (!userId || !isOpen) {
        setUserInfo(null);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const user = await getUserById(userId);
        setUserInfo(user);
      } catch (error) {
        console.error(`Error loading ${role.toLowerCase()} info:`, error);
        setUserInfo(null);
      } finally {
        setLoading(false);
      }
    };

    loadUserInfo();
  }, [userId, role, isOpen]);

  return (
    <div className="p-4 backdrop-blur-sm rounded-2xl border border-[#3a4a5c]/30 shadow-lg hover:shadow-xl transition-all duration-200 hover:bg-[#232e3c]/70">
      {loading ? (
        <div className="flex flex-col items-center justify-center gap-3 py-8">
          <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
          <span className="text-[#708499] text-sm">Loading...</span>
        </div>
      ) : userInfo ? (
        <div className="flex flex-col items-center text-center space-y-4 py-8">
          <div className="px-3 py-1 bg-[#6ab2f2]/10 rounded-full border border-[#6ab2f2]/20">
            <p className="text-[#6ab2f2] text-xs font-semibold uppercase tracking-wider">
              {role}
            </p>
          </div>

          <div className="relative">
            {userInfo.photoURL ? (
              <Avatar
                size={48}
                src={userInfo.photoURL}
                className="ring-2 ring-[#6ab2f2]/30 shadow-lg"
              />
            ) : (
              <div className="w-12 h-12 bg-gradient-to-br from-[#3a4a5c] to-[#2a3441] rounded-full flex items-center justify-center ring-2 ring-[#6ab2f2]/30 shadow-lg">
                <User className="w-6 h-6 text-[#708499]" />
              </div>
            )}
          </div>

          <div className="space-y-1">
            <p className="text-[#f5f5f5] font-semibold text-base leading-tight">
              {userInfo.role === 'admin'
                ? ADMIN_DEFAULT_NAME
                : (userInfo.displayName ??
                  userInfo.name ??
                  t(orderActorsMessages.anonymousUser))}
            </p>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center text-center space-y-4 py-8">
          <div className="px-3 py-1 bg-[#6ab2f2]/10 rounded-full border border-[#6ab2f2]/20">
            <p className="text-[#6ab2f2] text-xs font-semibold uppercase tracking-wider">
              {role}
            </p>
          </div>
          <div className="w-12 h-12 bg-gradient-to-br from-[#3a4a5c] to-[#2a3441] rounded-full flex items-center justify-center ring-2 ring-[#6ab2f2]/30 shadow-lg">
            <User className="w-6 h-6 text-[#708499]" />
          </div>
          <p className="text-[#708499] text-sm">
            {t(orderActorsMessages.noRoleAssigned, {
              role: role.toLowerCase(),
            })}
          </p>
        </div>
      )}
    </div>
  );
}

export function OrderActors({
  buyerId,
  sellerId,
  isOpen,
  isResellOrder,
}: OrderActorsProps) {
  const hasBuyer = Boolean(buyerId);
  const hasSeller = Boolean(sellerId);

  if (!hasBuyer && !hasSeller) {
    return null;
  }

  const hasOnlyOne = (hasBuyer && !hasSeller) || (!hasBuyer && hasSeller);
  const hasBoth = hasBuyer && hasSeller;

  return (
    <div
      className={cn(
        'grid gap-4',
        hasOnlyOne && 'grid-cols-1 justify-center',
        hasBoth && 'grid-cols-2 md:grid-cols-2',
      )}
    >
      {hasBuyer && (
        <UserInfoSection
          userId={buyerId!}
          role={isResellOrder ? 'Resseller' : 'Buyer'}
          isOpen={isOpen}
        />
      )}
      {hasSeller && (
        <UserInfoSection userId={sellerId!} role={'Seller'} isOpen={isOpen} />
      )}
    </div>
  );
}
