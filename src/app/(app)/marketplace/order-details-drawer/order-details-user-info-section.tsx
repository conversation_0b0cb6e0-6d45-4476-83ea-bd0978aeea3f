import { Caption } from '@telegram-apps/telegram-ui';
import { Loader2, User } from 'lucide-react';

import type { UserEntity } from '@/constants/core.constants';
import { ADMIN_DEFAULT_NAME } from '@/constants/core.constants';

interface OrderDetailsUserInfoSectionProps {
  userInfo: UserEntity | null;
  loading: boolean;
  userLabel: string;
}

export function OrderDetailsUserInfoSection({
  userInfo,
  loading,
  userLabel,
}: OrderDetailsUserInfoSectionProps) {
  return (
    <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30 flex items-center justify-between">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-[#6ab2f2] rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        <span className="text-[#f5f5f5] font-medium">{userLabel}</span>
      </div>
      {loading ? (
        <div className="flex items-center gap-2 text-[#708499]">
          <Loader2 className="w-4 h-4 animate-spin" />
          <Caption level="2" weight="3">
            Loading...
          </Caption>
        </div>
      ) : userInfo ? (
        <div className="space-y-1">
          <p className="font-medium text-[#f5f5f5]">
            {userInfo.role === 'admin'
              ? ADMIN_DEFAULT_NAME
              : userInfo.displayName || userInfo.email || 'Anonymous User'}
          </p>
          {userInfo.email &&
            userInfo.displayName &&
            userInfo.role !== 'admin' && (
              <Caption level="2" weight="3" className="text-[#708499]">
                {userInfo.email}
              </Caption>
            )}
        </div>
      ) : (
        <Caption level="2" weight="3" className="text-[#708499]">
          Anonymous User
        </Caption>
      )}
    </div>
  );
}
