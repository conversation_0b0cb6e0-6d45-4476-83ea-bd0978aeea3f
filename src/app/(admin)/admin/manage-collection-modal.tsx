'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import {
  clearOrderDeadlines,
  recalculateOrderDeadlines,
} from '@/api/admin-api';
import { createCollection, updateCollection } from '@/api/collection-api';
import { Button } from '@/components/ui/button';
import { DateTimePicker } from '@/components/ui/date-picker';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import type { CollectionEntity } from '@/constants/core.constants';
import {
  COLLECTION_STATUS_TEXT,
  CollectionStatus,
} from '@/constants/core.constants';
import { firebaseTimestampToDate } from '@/utils/date-utils';

const collectionSchema = z.object({
  id: z.string().min(1, 'Collection ID is required'),
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  status: z.nativeEnum(CollectionStatus),
  floorPrice: z.number().min(0, 'Floor price must be non-negative'),
  launchedAt: z.date().optional(),
  active: z.boolean(),
  lock_period: z
    .number()
    .min(1, 'Lock period must be at least 1 day')
    .optional(),
});

type CollectionFormData = z.infer<typeof collectionSchema>;

interface ManageCollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  collection: CollectionEntity | null;
  onSave: () => void;
}

export const ManageCollectionModal = ({
  isOpen,
  onClose,
  collection,
  onSave,
}: ManageCollectionModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors },
  } = useForm<CollectionFormData>({
    resolver: zodResolver(collectionSchema),
    defaultValues: {
      id: '',
      name: '',
      description: '',
      status: CollectionStatus.PREMARKET,
      floorPrice: 0,
      launchedAt: undefined,
      active: true,
      lock_period: 21,
    },
  });

  useEffect(() => {
    if (collection) {
      const launchedAtDate = collection.launchedAt
        ? firebaseTimestampToDate(collection.launchedAt)
        : undefined;

      reset({
        id: collection.id,
        name: collection.name,
        description: collection.description,
        status: collection.status,
        floorPrice: collection.floorPrice || 0,
        launchedAt: launchedAtDate,
        active: collection.active ?? true,
        lock_period: collection.lock_period,
      });
    } else {
      reset({
        id: '',
        name: '',
        description: '',
        status: CollectionStatus.PREMARKET,
        floorPrice: 0,
        launchedAt: undefined,
        active: true,
        lock_period: undefined,
      });
    }
  }, [collection, reset]);

  const onSubmit = async (data: CollectionFormData) => {
    setIsSubmitting(true);
    try {
      // Handle launchedAt field logic
      const updateData: Partial<CollectionEntity> = { ...data };
      const isUpdatingExistingCollection = !!collection;
      const originalLaunchedAt = collection?.launchedAt;

      // Convert launchedAt string to Date if provided, or explicitly set to null if undefined
      if (data.launchedAt) {
        updateData.launchedAt = new Date(data.launchedAt);
      } else {
        // Explicitly set to null when clearing the field
        updateData.launchedAt = null as any;
      }

      // If changing status from PREMARKET to MARKET and launchedAt is not set
      if (
        !data.launchedAt &&
        data.status === CollectionStatus.MARKET &&
        collection?.status === CollectionStatus.PREMARKET &&
        !collection.launchedAt
      ) {
        updateData.launchedAt = new Date();
      }

      // Save the collection first
      if (collection) {
        await updateCollection(collection.id, updateData);
      } else {
        await createCollection(data);
      }

      // Handle deadline recalculation for existing collections only
      if (isUpdatingExistingCollection && collection) {
        const newLaunchedAt = updateData.launchedAt;
        const launchedAtChanged =
          (originalLaunchedAt
            ? firebaseTimestampToDate(originalLaunchedAt)?.getTime()
            : null) !== (newLaunchedAt?.getTime?.() || null);

        if (launchedAtChanged) {
          try {
            if (newLaunchedAt) {
              // LaunchedAt was set or changed - recalculate deadlines
              await recalculateOrderDeadlines(collection.id);
              console.log('Order deadlines recalculated successfully');
            } else {
              // LaunchedAt was cleared - clear all deadlines
              await clearOrderDeadlines(collection.id);
              console.log('Order deadlines cleared successfully');
            }
          } catch (deadlineError) {
            console.error('Error updating order deadlines:', deadlineError);
            // Don't fail the entire operation if deadline update fails
          }
        }
      }

      onSave();
      onClose();
    } catch (error) {
      console.error('Error saving collection:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {collection ? 'Edit Collection' : 'Add Collection'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="id">Collection ID</Label>
            <Input
              id="id"
              {...register('id')}
              placeholder="Enter collection ID"
            />
            {errors.id && (
              <p className="text-sm text-red-600">{errors.id.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="Enter collection name"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter collection description"
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-red-600">
                {errors.description.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="floorPrice">Floor Price (TON)</Label>
            <Input
              id="floorPrice"
              type="number"
              step="0.01"
              min="0"
              {...register('floorPrice', { valueAsNumber: true })}
              placeholder="Enter minimum floor price"
            />
            {errors.floorPrice && (
              <p className="text-sm text-red-600">
                {errors.floorPrice.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="lock_period">Lock Period (days)</Label>
            <Input
              id="lock_period"
              type="number"
              step="1"
              min="1"
              {...register('lock_period', { valueAsNumber: true })}
              placeholder="Enter lock period in days (optional)"
            />
            <p className="text-sm text-gray-500">
              Collection-specific lock period. Leave empty to use app default
              (21 days)
            </p>
            {errors.lock_period && (
              <p className="text-sm text-red-600">
                {errors.lock_period.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={watch('status')}
              onValueChange={(value) =>
                setValue('status', value as CollectionStatus)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(CollectionStatus).map((status) => (
                  <SelectItem key={status} value={status}>
                    {COLLECTION_STATUS_TEXT[status]}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.status && (
              <p className="text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="active">Active</Label>
              <Switch
                id="active"
                checked={watch('active')}
                onCheckedChange={(checked) => setValue('active', checked)}
              />
            </div>
            <p className="text-sm text-gray-500">
              When inactive, users cannot create orders for this collection
            </p>
            {errors.active && (
              <p className="text-sm text-red-600">{errors.active.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="launchedAt">Launched At</Label>
              {watch('launchedAt') && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setValue('launchedAt', undefined)}
                  className="h-6 px-2 text-xs"
                >
                  Clear
                </Button>
              )}
            </div>
            <DateTimePicker
              date={watch('launchedAt')}
              onDateChange={(date) => setValue('launchedAt', date)}
              placeholder="Select launch date and time"
            />
            <p className="text-sm text-gray-500">
              Clear this field to set launchedAt to null
            </p>
            {errors.launchedAt && (
              <p className="text-sm text-red-600">
                {errors.launchedAt.message}
              </p>
            )}
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : collection ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
