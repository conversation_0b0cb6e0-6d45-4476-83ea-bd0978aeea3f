'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Role } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';

import { AdminOrdersManagement } from './admin-orders-management';
import { CollectionManagement } from './collection-management';
import { CustomReferralManagement } from './custom-referral-management';
import { FeesManagement } from './fees-management';
import { RevenueDisplay } from './revenue-display';
import { StarsCountDisplay } from './stars-count-display';
import { TopUpUserBalance } from './top-up-user-balance';

export default function Admin() {
  const { currentUser } = useRootContext();
  const router = useRouter();

  useEffect(() => {
    document.documentElement.classList.add('dark');
  }, []);

  useEffect(() => {
    if (currentUser && currentUser.role !== Role.ADMIN) {
      router.replace('/');
    }
  }, [currentUser, router]);

  if (currentUser?.role !== Role.ADMIN) {
    return null;
  }

  return (
    <div className="mx-auto w-full max-w-6xl p-4">
      <Tabs defaultValue="revenue" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="revenue">Revenue & Fees</TabsTrigger>
          <TabsTrigger value="collections">Collections</TabsTrigger>
          <TabsTrigger value="referrals">Custom Referrals</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="revenue" className="mt-6">
          <div className="space-y-6">
            <FeesManagement />
            <TopUpUserBalance />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <RevenueDisplay />
              <StarsCountDisplay />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="referrals" className="mt-6">
          <CustomReferralManagement />
        </TabsContent>

        <TabsContent value="collections" className="mt-6">
          <CollectionManagement />
        </TabsContent>

        <TabsContent value="orders" className="mt-6">
          <AdminOrdersManagement />
        </TabsContent>
      </Tabs>
    </div>
  );
}
