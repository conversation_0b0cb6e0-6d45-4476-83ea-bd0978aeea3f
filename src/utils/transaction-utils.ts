import type { TxType } from '@/constants/core.constants';
import { TX_TYPE_DISPLAY_MAP } from '@/constants/core.constants';
import { firebaseTimestampToDate } from '@/utils/date-utils';
import {
  formatTransactionAmount,
  getTransactionAmountColor,
} from '@/utils/transaction-sign-utils';

export const formatTxType = (txType: TxType): string => {
  return TX_TYPE_DISPLAY_MAP[txType] || txType;
};

export const formatAmount = (amount: number): string => {
  return formatTransactionAmount(amount);
};

export const getAmountColor = (amount: number): string => {
  return getTransactionAmountColor(amount);
};

export const formatTransactionDate = (date: Date | undefined): string => {
  if (!date) return '';
  return firebaseTimestampToDate(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};
