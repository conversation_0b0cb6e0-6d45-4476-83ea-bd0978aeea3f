const REFERRAL_ID_KEY = 'marketplace_referral_id';

export function extractReferralIdFromUrl(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const urlParams = new URLSearchParams(window.location.search);

  let refParam = urlParams.get('tgWebAppStartParam');
  if (refParam?.startsWith('ref_')) {
    refParam = refParam.replace('ref_', '');
  }
  return refParam;
}

export function extractReferralIdFromTelegram(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  const telegramWebApp = window.Telegram?.WebApp;

  if (telegramWebApp) {
    let startParam: string | null | undefined =
      telegramWebApp.initDataUnsafe?.start_param;

    if (!startParam) {
      const urlParams = new URLSearchParams(window.location.search);
      startParam = urlParams.get('tgWebAppStartParam');
    }

    if (startParam?.startsWith('ref_')) {
      return startParam.replace('ref_', '');
    }
  }

  return null;
}

export function storeReferralId(referralId: string): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(REFERRAL_ID_KEY, referralId);
    console.log('Referral ID stored:', referralId);
  } catch (error) {
    console.error('Failed to store referral ID:', error);
  }
}

export function getStoredReferralId(): string | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    return localStorage.getItem(REFERRAL_ID_KEY);
  } catch (error) {
    console.error('Failed to get stored referral ID:', error);
    return null;
  }
}

export function clearStoredReferralId(): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(REFERRAL_ID_KEY);
    console.log('Referral ID cleared');
  } catch (error) {
    console.error('Failed to clear referral ID:', error);
  }
}

export function handleReferralFromUrl(): string | null {
  // Try to get referral ID from URL parameters first, fallback to Telegram start parameters
  let referralId = extractReferralIdFromUrl();
  referralId ??= extractReferralIdFromTelegram();

  if (referralId) {
    // Only store if we don't already have one stored
    const existingReferralId = getStoredReferralId();
    if (!existingReferralId) {
      storeReferralId(referralId);
      console.log('New referral ID detected and stored:', referralId);
    } else {
      console.log(
        'Referral ID already exists, not overwriting:',
        existingReferralId,
      );
    }
    return referralId;
  }

  return null;
}

export function generateReferralLink(userId: string): string {
  const botName = process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME;

  if (!botName) {
    throw new Error(
      'Bot name and app name must be configured in environment variables',
    );
  }

  return `https://t.me/${botName}?startapp=ref_${userId}`;
}

export function consumeReferralId(): string | null {
  const referralId = getStoredReferralId();
  if (referralId) {
    clearStoredReferralId();
    console.log('Referral ID consumed:', referralId);
  }
  return referralId;
}
