import type { IntlShape } from 'react-intl';
import { toast } from 'sonner';

import {
  makePurchaseAsBuyer,
  makePurchaseAsSeller,
  makeSecondaryMarketPurchase,
} from '@/api/orders-api';
import { UserType } from '@/constants/core.constants';
import { formatError } from '@/utils/error-handler';

export interface OrderActionResult {
  success: boolean;
  message?: string;
}

export async function executeMarketplaceOrderAction(
  orderId: string,
  userType: UserType,
  formatMessage: IntlShape['formatMessage'],
): Promise<OrderActionResult> {
  try {
    let result;
    if (userType === UserType.BUYER) {
      result = await makePurchaseAsSeller(orderId);
    } else {
      result = await makePurchaseAsBuyer(orderId);
    }

    const message = result.message ?? 'Action completed successfully!';
    toast.success(message);
    return { success: true, message };
  } catch (error: unknown) {
    console.error('Marketplace action failed:', error);
    const errorMessage = formatError(error, formatMessage);
    toast.error(errorMessage);
    return { success: false, message: errorMessage };
  }
}

export async function executeSecondaryMarketPurchase(
  orderId: string,
  formatMessage: IntlShape['formatMessage'],
): Promise<OrderActionResult> {
  try {
    const result = await makeSecondaryMarketPurchase(orderId);
    const message =
      result.message ?? 'Secondary market purchase completed successfully!';
    toast.success(message);
    return { success: true, message };
  } catch (error: unknown) {
    console.error('Secondary market purchase failed:', error);
    const errorMessage = formatError(error, formatMessage);
    toast.error(errorMessage);
    return { success: false, message: errorMessage };
  }
}
