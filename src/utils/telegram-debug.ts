/**
 * Debug utilities for Telegram Mini App integration
 */

export function debugTelegramEnvironment() {
  const debug = {
    isTelegram: false,
    webAppAvailable: false,
    initData: null as any,
    platform: 'unknown',
    version: 'unknown',
    startParam: null as string | null,
    botName: process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME,
    errors: [] as string[],
  };

  try {
    // Check if we're in Telegram
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      debug.isTelegram = true;
      debug.webAppAvailable = true;

      const webApp = window.Telegram.WebApp;
      debug.platform = webApp.platform;
      debug.version = webApp.version;
      debug.initData = webApp.initDataUnsafe;
      debug.startParam = webApp.initDataUnsafe?.start_param || null;
    } else {
      debug.errors.push('Telegram WebApp not available');
    }
  } catch (error) {
    debug.errors.push(`Error accessing Telegram: ${error}`);
  }

  return debug;
}

export function testOrderShareLink(orderId: string) {
  const botName = process.env.NEXT_PUBLIC_TELEGRAM_BOT_NAME;

  if (!botName) {
    console.error('❌ NEXT_PUBLIC_TELEGRAM_BOT_NAME not configured');
    return null;
  }

  const shareLink = `https://t.me/${botName}?startapp=order_${orderId}`;

  return shareLink;
}

export function validateShareLinkFormat(shareLink: string): boolean {
  // Validate the share link format
  const telegramLinkRegex = /^https:\/\/t\.me\/[a-zA-Z0-9_]+\?startapp=.+$/;
  return telegramLinkRegex.test(shareLink);
}

export function extractBotNameFromLink(shareLink: string): string | null {
  const match = shareLink.match(/https:\/\/t\.me\/([a-zA-Z0-9_]+)\?/);
  return match ? match[1] : null;
}

export function extractStartParamFromLink(shareLink: string): string | null {
  const match = shareLink.match(/startapp=([^&]+)/);
  return match ? match[1] : null;
}
