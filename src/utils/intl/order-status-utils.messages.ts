import { defineMessages } from 'react-intl';

export const orderStatusUtilsMessages = defineMessages({
  sellerMustSend: {
    id: 'orderStatusUtils.sellerMustSend',
    defaultMessage: 'Seller must send',
  },
  sendOrLoseCollateral: {
    id: 'orderStatusUtils.sendOrLoseCollateral',
    defaultMessage: 'Send or lose collateral',
  },
  sendGiftToRelayerOrLoseCollateral: {
    id: 'orderStatusUtils.sendGiftToRelayerOrLoseCollateral',
    defaultMessage: 'Send gift to relayer or lose collateral',
  },
  sellerMustSendGiftOrLoseCollateral: {
    id: 'orderStatusUtils.sellerMustSendGiftOrLoseCollateral',
    defaultMessage: 'Seller must send gift or lose collateral',
  },
  claimGiftFromRelayerOrLoseCollateral: {
    id: 'orderStatusUtils.claimGiftFromRelayerOrLoseCollateral',
    defaultMessage: 'Claim gift from relayer or lose collateral',
  },
  buyerMustClaimGiftOrLoseCollateral: {
    id: 'orderStatusUtils.buyerMustClaimGiftOrLoseCollateral',
    defaultMessage: 'Buyer must claim gift or lose collateral',
  },
});
