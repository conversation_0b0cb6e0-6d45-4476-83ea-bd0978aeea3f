import { useCallback } from 'react';

import { getUserById } from '@/api/auth-api';
import { updateUser } from '@/api/user-api';
import type { UserEntity } from '@/constants/core.constants';
import {
  clearStoredReferralId,
  getStoredReferralId,
} from '@/utils/referral-utils';

interface UseReferralProcessorReturn {
  processReferralForUser: (
    user: UserEntity,
    firebaseUid: string,
    onUserUpdated?: (updatedUser: UserEntity) => void,
  ) => Promise<UserEntity | null>;
}

export const useReferralProcessor = (): UseReferralProcessorReturn => {
  const processReferralForUser = useCallback(
    async (
      user: UserEntity,
      firebaseUid: string,
      onUserUpdated?: (updatedUser: UserEntity) => void,
    ): Promise<UserEntity | null> => {
      try {
        // Only process if user doesn't have a referrer_id
        if (!user.referrer_id) {
          const storedReferralId = getStoredReferralId();

          // Validate stored referral ID exists and is not the user's own ID
          if (storedReferralId && storedReferralId !== user.id) {
            console.log('Processing stored referral ID:', storedReferralId);

            // Update user with referral ID
            await updateUser(user.id, { referrer_id: storedReferralId });

            // Clear stored referral ID since it's been processed
            clearStoredReferralId();
            console.log('Referral ID processed and cleared from storage');

            // Refetch user to get updated data with the new referrer_id
            const updatedUser = await getUserById(firebaseUid);

            // Notify caller about the updated user if callback provided
            if (updatedUser && onUserUpdated) {
              onUserUpdated(updatedUser);
            }

            return updatedUser;
          }
        }

        // Return original user if no processing needed
        return user;
      } catch (error) {
        console.error('Error processing referral ID:', error);
        // Return original user on error to maintain stability
        return user;
      }
    },
    [],
  );

  return {
    processReferralForUser,
  };
};
