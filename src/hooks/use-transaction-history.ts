import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useEffect, useRef, useState } from 'react';

import type { UserTxEntity } from '@/constants/core.constants';
import { useRootContext } from '@/root-context';
import { getUserTransactionHistory } from '@/services/transaction-history-service';

export function useTransactionHistory() {
  const { currentUser } = useRootContext();
  const [transactions, setTransactions] = useState<UserTxEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const lastDocRef = useRef<DocumentSnapshot | null>(null);

  const loadTransactions = useCallback(
    async (isLoadMore = false) => {
      if (!currentUser?.id) return;

      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setTransactions([]);
        lastDocRef.current = null;
      }

      try {
        const result = await getUserTransactionHistory({
          userId: currentUser.id,
          limit: 20,
          lastDoc: isLoadMore ? lastDocRef.current : null,
        });

        if (isLoadMore) {
          setTransactions((prev) => {
            // Deduplicate transactions by ID
            const existingIds = new Set(prev.map((tx) => tx.id));
            const newTransactions = result.transactions.filter(
              (tx) => !existingIds.has(tx.id),
            );
            return [...prev, ...newTransactions];
          });
        } else {
          setTransactions(result.transactions);
        }

        lastDocRef.current = result.lastDoc ?? null;
        setHasMore(result.hasMore);
      } catch (error) {
        console.error('Error loading transaction history:', error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [currentUser?.id],
  );

  const handleRefresh = useCallback(() => {
    setRefreshing(true);
    loadTransactions().finally(() => setRefreshing(false));
  }, [loadTransactions]);

  const loadMore = useCallback(() => {
    loadTransactions(true);
  }, [loadTransactions]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  return {
    transactions,
    loading,
    loadingMore,
    hasMore,
    refreshing,
    handleRefresh,
    loadMore,
  };
}
