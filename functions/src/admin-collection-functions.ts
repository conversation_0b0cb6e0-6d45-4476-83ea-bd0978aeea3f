import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { CollectionEntity } from "./types";
import { CORS_CONFIG } from "./config";
import { log } from "./utils/logger";
import { calculateOrderDeadline } from "./services/deadline-service";

const db = admin.firestore();

interface RecalculateDeadlinesRequest {
  collectionId: string;
}

interface ClearDeadlinesRequest {
  collectionId: string;
}

async function validateAdminUser(uid: string): Promise<void> {
  const userDoc = await db.collection("users").doc(uid).get();
  if (!userDoc.exists || userDoc.data()?.role !== "admin") {
    throw new HttpsError(
      "permission-denied",
      "Only admin users can perform this operation."
    );
  }
}

async function batchUpdateOrders(
  orders: admin.firestore.QueryDocumentSnapshot[],
  updateFields: Record<string, any>,
  operation: string,
  collectionId: string
): Promise<number> {
  const BATCH_SIZE = 400;
  let totalUpdatedCount = 0;

  for (let i = 0; i < orders.length; i += BATCH_SIZE) {
    const chunk = orders.slice(i, i + BATCH_SIZE);
    const batch = db.batch();

    for (const orderDoc of chunk) {
      batch.update(orderDoc.ref, {
        ...updateFields,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    }

    await batch.commit();
    totalUpdatedCount += chunk.length;

    log.info(`Processed batch for ${operation}`, {
      collectionId,
      batchNumber: Math.floor(i / BATCH_SIZE) + 1,
      updatedCount: chunk.length,
      operation,
    });
  }

  return totalUpdatedCount;
}

async function processDeadlineOperation(
  request: any,
  operation: "recalculate" | "clear"
) {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { collectionId } = request.data;
  if (!collectionId) {
    throw new HttpsError("invalid-argument", "Collection ID is required.");
  }

  await validateAdminUser(request.auth.uid);

  let ordersQuery;
  let updateFields;
  let emptyMessage;
  let successMessage;
  let logOperation;

  if (operation === "recalculate") {
    const collectionDoc = await db
      .collection("collections")
      .doc(collectionId)
      .get();
    if (!collectionDoc.exists) {
      throw new HttpsError("not-found", "Collection not found.");
    }

    const collection = collectionDoc.data() as CollectionEntity;
    const newDeadline = await calculateOrderDeadline(collection);

    ordersQuery = await db
      .collection("orders")
      .where("collectionId", "==", collectionId)
      .where("status", "==", "paid")
      .get();

    updateFields = { deadline: newDeadline };
    emptyMessage = "No paid orders found for this collection.";
    successMessage = (count: number) =>
      `Successfully updated ${count} order deadlines.`;
    logOperation = "recalculate_deadlines";

    return {
      ordersQuery,
      updateFields,
      emptyMessage,
      successMessage,
      logOperation,
      extraData: { newDeadline: newDeadline?.toDate() },
    };
  } else {
    ordersQuery = await db
      .collection("orders")
      .where("collectionId", "==", collectionId)
      .where("deadline", "!=", null)
      .get();

    updateFields = { deadline: null };
    emptyMessage = "No orders with deadlines found for this collection.";
    successMessage = (count: number) =>
      `Successfully cleared ${count} order deadlines.`;
    logOperation = "clear_deadlines";

    return {
      ordersQuery,
      updateFields,
      emptyMessage,
      successMessage,
      logOperation,
      extraData: {},
    };
  }
}

export const recalculateOrderDeadlines = onCall<RecalculateDeadlinesRequest>(
  { cors: CORS_CONFIG },
  async (request) => {
    const { collectionId } = request.data;

    try {
      const {
        ordersQuery,
        updateFields,
        emptyMessage,
        successMessage,
        logOperation,
        extraData,
      } = await processDeadlineOperation(request, "recalculate");

      if (ordersQuery.empty) {
        log.info("No paid orders found for collection", {
          collectionId,
          operation: logOperation,
        });
        return {
          success: true,
          message: emptyMessage,
          updatedCount: 0,
        };
      }

      const totalUpdatedCount = await batchUpdateOrders(
        ordersQuery.docs,
        updateFields,
        logOperation,
        collectionId
      );

      log.info("Recalculated deadlines for collection orders", {
        collectionId,
        totalUpdatedCount,
        newDeadline: extraData.newDeadline,
        operation: logOperation,
      });

      return {
        success: true,
        message: successMessage(totalUpdatedCount),
        updatedCount: totalUpdatedCount,
        ...extraData,
      };
    } catch (error) {
      log.error("Error recalculating order deadlines", error, {
        collectionId,
        operation: "recalculate_deadlines",
      });
      throw new HttpsError(
        "internal",
        (error as any).message ??
          "Server error while recalculating order deadlines."
      );
    }
  }
);

export const clearOrderDeadlines = onCall<ClearDeadlinesRequest>(
  { cors: CORS_CONFIG },
  async (request) => {
    const { collectionId } = request.data;

    try {
      const {
        ordersQuery,
        updateFields,
        emptyMessage,
        successMessage,
        logOperation,
      } = await processDeadlineOperation(request, "clear");

      if (ordersQuery.empty) {
        log.info("No orders with deadlines found for collection", {
          collectionId,
          operation: logOperation,
        });
        return {
          success: true,
          message: emptyMessage,
          updatedCount: 0,
        };
      }

      const totalUpdatedCount = await batchUpdateOrders(
        ordersQuery.docs,
        updateFields,
        logOperation,
        collectionId
      );

      log.info("Cleared deadlines for collection orders", {
        collectionId,
        totalUpdatedCount,
        operation: logOperation,
      });

      return {
        success: true,
        message: successMessage(totalUpdatedCount),
        updatedCount: totalUpdatedCount,
      };
    } catch (error) {
      log.error("Error clearing order deadlines", error, {
        collectionId,
        operation: "clear_deadlines",
      });
      throw new HttpsError(
        "internal",
        (error as any).message ?? "Server error while clearing order deadlines."
      );
    }
  }
);
