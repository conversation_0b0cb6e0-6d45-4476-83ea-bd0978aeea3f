import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import {
  requireAuthentication,
  validateBuyerOwnership,
  validateOrderCreationParams,
  validatePurchaseParams,
} from "../services/auth-middleware";
import { createOrder } from "../services/order-creation-service";
import { processPurchase } from "../services/purchase-flow-service";
import { UserType } from "../types";
import { CORS_CONFIG } from "../config";
import { log } from "../utils/logger";
import { GENERIC_ERRORS } from "../constants/error-messages";

export const createOrderAsBuyer = onCall<{
  buyerId: string;
  collectionId: string;
  price: number;
  owned_gift_id: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const authRequest = requireAuthentication(request);
  const { buyerId, collectionId, price, owned_gift_id } = request.data;

  validateOrderCreationParams(request.data, UserType.BUYER);
  validateBuyerOwnership(authRequest, buyerId);

  try {
    const db = admin.firestore();

    const result = await createOrder(db, {
      userId: buyerId,
      collectionId,
      price,
      owned_gift_id,
      userType: UserType.BUYER,
      secondaryMarketPrice: null,
    });

    return result;
  } catch (error) {
    log.error("Error creating order as buyer", error, {
      operation: "create_order_as_buyer",
      requestData: request.data,
      userId: request.auth?.uid,
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while creating order.",
      })
    );
  }
});

export const makePurchaseAsBuyer = onCall<{
  buyerId: string;
  orderId: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const authRequest = requireAuthentication(request);
  const { buyerId, orderId } = request.data;

  validatePurchaseParams(request.data, UserType.BUYER);
  validateBuyerOwnership(authRequest, buyerId);

  try {
    const db = admin.firestore();

    const result = await processPurchase(db, {
      userId: buyerId,
      orderId,
      userType: UserType.BUYER,
    });

    return result;
  } catch (error) {
    log.error("Error making purchase as buyer", error, {
      buyerId,
      orderId,
      operation: "buyer_purchase",
    });
    throw new HttpsError(
      "internal",
      JSON.stringify({
        errorKey: GENERIC_ERRORS.SERVER_ERROR,
        fallbackMessage:
          (error as any).message ?? "Server error while making purchase.",
      })
    );
  }
});
