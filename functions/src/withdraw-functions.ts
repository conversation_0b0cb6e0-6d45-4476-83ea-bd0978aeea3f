import { HttpsError, onCall } from "firebase-functions/v2/https";
import { CORS_CONFIG } from "./config";
import { WITHDRAWAL_ERRORS } from "./constants/error-messages";
import {
  getUserData,
  requireAuthentication,
  requireTonWallet,
  validatePositiveAmount,
} from "./services/auth-middleware";
import {
  hasAvailableBalance,
  spendFundsWithHistory,
} from "./services/balance-service";
import { applyWithdrawFee, getAppConfig } from "./services/fee-service";
import { getTonWalletService } from "./services/ton-wallet-service";
import {
  checkWithdrawalLimit,
  updateWithdrawalTracking,
} from "./services/withdrawal-limit-service";
import { TxType } from "./types";
import { safeSubtract } from "./utils";
import { log } from "./utils/logger";

export const withdrawFunds = onCall<{
  amount: number;
}>({ cors: CORS_CONFIG }, async (request) => {
  const authRequest = requireAuthentication(request);
  const { amount } = request.data;

  validatePositiveAmount({ amount });

  try {
    const userId = authRequest.auth.uid;

    const user = await getUserData(userId);
    requireTonWallet(user);

    const appConfig = await getAppConfig();
    if (appConfig) {
      const minWithdrawal = appConfig.min_withdrawal_amount || 0;
      const maxWithdrawal24h =
        appConfig.max_withdrawal_amount || Number.MAX_SAFE_INTEGER;

      if (amount < minWithdrawal) {
        throw new HttpsError(
          "failed-precondition",
          JSON.stringify({
            errorKey: WITHDRAWAL_ERRORS.AMOUNT_BELOW_MINIMUM,
            params: { minAmount: minWithdrawal },
            fallbackMessage: `Withdrawal amount must be at least ${minWithdrawal} TON.`,
          })
        );
      }

      // Check 24-hour withdrawal limit
      const withdrawalLimitInfo = await checkWithdrawalLimit({
        userId,
        requestedAmount: amount,
        maxWithdrawalAmount: maxWithdrawal24h,
      });

      if (!withdrawalLimitInfo.canWithdraw) {
        throw new HttpsError(
          "failed-precondition",
          JSON.stringify({
            errorKey: WITHDRAWAL_ERRORS.AMOUNT_EXCEEDS_24H_LIMIT,
            params: {
              requestedAmount: amount,
              remainingLimit: withdrawalLimitInfo.remainingLimit,
              resetAt: withdrawalLimitInfo.resetAt.toISOString(),
            },
            fallbackMessage: `Withdrawal amount exceeds 24-hour limit. You can withdraw up to ${
              withdrawalLimitInfo.remainingLimit
            } TON. Limit resets at ${withdrawalLimitInfo.resetAt.toLocaleString()}.`,
          })
        );
      }
    }

    const hasBalance = await hasAvailableBalance(userId, amount);
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        JSON.stringify({
          errorKey: WITHDRAWAL_ERRORS.INSUFFICIENT_AVAILABLE_BALANCE,
          fallbackMessage: "Insufficient available balance for withdrawal.",
        })
      );
    }

    const feeAmount = await applyWithdrawFee(userId, amount);
    const netAmountToUser = safeSubtract(amount, feeAmount);

    if (netAmountToUser <= 0) {
      throw new HttpsError(
        "failed-precondition",
        JSON.stringify({
          errorKey: WITHDRAWAL_ERRORS.AMOUNT_TOO_SMALL_AFTER_FEES,
          fallbackMessage: "Amount too small after fees.",
        })
      );
    }

    await spendFundsWithHistory({
      userId,
      amount,
      txType: TxType.WITHDRAW,
      description: `Withdrawal to TON wallet (gross: ${amount} TON, net: ${netAmountToUser} TON, fee: ${feeAmount} TON)`,
    });

    const tonWalletService = getTonWalletService();
    const transferResult = await tonWalletService.sendWithdrawal(
      netAmountToUser,
      user.ton_wallet_address!
    );

    // Update 24-hour withdrawal tracking
    await updateWithdrawalTracking({
      userId,
      withdrawnAmount: amount,
    });

    log.info(
      `Withdrawal processed: ${netAmountToUser} TON sent to ${user.ton_wallet_address} (${feeAmount} TON fee applied)`,
      {
        operation: "withdrawal",
        userId,
        netAmountToUser,
        feeAmount,
        walletAddress: user.ton_wallet_address,
      }
    );

    return {
      success: transferResult.success,
      message: `Withdrawal successful. ${netAmountToUser} TON sent to your wallet (${feeAmount} TON fee applied)`,
      netAmount: netAmountToUser,
      feeAmount,
      transactionHash: transferResult.transactionHash,
    };
  } catch (error) {
    log.error("Error processing withdrawal", error, {
      operation: "withdrawal",
      userId: request.auth?.uid,
      requestData: request.data,
    });
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while processing withdrawal."
    );
  }
});
